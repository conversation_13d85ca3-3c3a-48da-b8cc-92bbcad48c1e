{"format": 1, "restore": {"D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\ClockWebClient.csproj": {}}, "projects": {"D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\ClockWebClient.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\ClockWebClient.csproj", "projectName": "ClockWebClient", "projectPath": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\ClockWebClient.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}, "https://nuget.telerik.com/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Google.Protobuf": {"target": "Package", "version": "[3.32.1, )"}, "Grpc.Net.Client": {"target": "Package", "version": "[2.71.0, )"}, "Grpc.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.72.0, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.305/PortableRuntimeIdentifierGraph.json"}}}}}