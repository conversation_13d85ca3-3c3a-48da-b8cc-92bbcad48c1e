syntax = "proto3";

option csharp_namespace = "GrpcServiceLearning";

package greet;

// The greeting service definition.
service Greeter {
  // Sends a greeting
  rpc <PERSON><PERSON><PERSON> (HelloRequest) returns (HelloReply);
}

// The request message containing the user's name.
message HelloRequest {
  string name = 1;
}

// The response message containing the greetings.
message HelloReply {
  string message = 1;
}
