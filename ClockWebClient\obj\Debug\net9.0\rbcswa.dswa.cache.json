{"GlobalPropertiesHash": "2ilJ2M8+ZdH0swl4cXFj9Ji8kay0R08ISE/fEc+OL0o=", "FingerprintPatternsHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["9kmoBB9xfGW/OWXGuYyjP4Eg1BjXGAUZkGBaoy0aU+w=", "94rjdp+2/c0uLi3LXv3fnlFYElxYYMtej8Zey+kOTRM=", "DsIMJcdsJASfpfN3kQTeYlQEddK/TDFkWAvLwbSkeBU=", "ng+w+FdS1b6TOc5EVkDlr3dZJ2XD6raZGPjoT1T9BUw=", "aPtFphY7KtAnkwaexMMIzxC5xmVGYfrTWSoBFxrC64Y=", "YZUSOnP98cRpbYqDKBcpjOFwg4pvA9FDxyoNIxZ9EQY=", "vpcSOf6r6jA5f+rpnWd1GwB93vQvZHnEDb2ipk77SC0=", "8adGv6ELmAXWDGvGrRMwsPo9Bcat+C7hBmRKMLd/Glk=", "cEasw6qk1kf3OZyYOpVId2513NJCZS80RoNWm+d9ffo=", "3pSOXME/0oLFLsVI2CXzrW7F6z4n8oCaTzM/RCmY2kA=", "7VEk2i+xZcx/cYpMDhdQ6VjNJEJZNqq4nkdPY1ND2Jc=", "RPSgvhoUMry/taCl8JyXQCZdTUXPerUZOnBri1qCvlA=", "W/uj+59IDBdqQM53eIw7sdjH9uSzGUbpeuU7wT8gEN0=", "rQOHFqY+4B5PR71oUBP+sC7lt+FEQzFa5tsbg0H+KHA=", "ycDN0xlY22p7+7+rmoRWtS2GRJjVEvKhSBzq78WyJpk=", "rMLaP9qOPJQ5ZfGuEHXNyz++l/eG6TVsnu8oT59Vxug=", "2fC+4W+2zySISJbkZA77tjdqI/uGnQ1ED7c498s83f0=", "gbpxIeLZwXBP3S7181EPF89w7BzYzea/7RGIhidSl2s=", "lqxhbE8FPKAjEhzjaMKQkc3pcCDOmd/nSZeGW3y+sPE=", "nol5sVQJSRb1SxC8QvoOKkQ0pYGrPua/+2VUDbIM2Sg=", "tsEMH1FKhL55pAA584U2K09SaKgSb22FM1qsRpv2FSs=", "kXyfCQvt10Hc9w2yHwTRtYRrNLh398s5K8Sggyu3ryw=", "ICkvz2F9bEZp8V7YTpaYnIAN6k7di+iMAZK/7/rtX9M=", "LSENTkC0KenHjU/rHsMDWa9nkrLzsuWxn64AUst4XA4=", "WwjRg3bsWggSXHVrlhukzoiV0/QHmiDyZxoFSQ/I4xY=", "Jjl8aeOk3FdnLRO/hGXUYRFOoUwCBro7/rofMox8FxI=", "n4T5Zn/Mg/WZX17t9idtCPkOKMyhlN+RB+8rD2oAcNY=", "HzZJvNFr3ob2dZzSLwSJV8zJvNN9hum/fx0tnR1dDWc=", "rsrDpvIthtwxQjTeh8b6R0hmQP3S2Q7tZFjnS8UUtPc=", "Aci2FO8qcNcoUSoChV4Z4ObyN+LdVL2h99H5QE1MF7w=", "dgz93bOjesu8p28KmBKAFlJ2AFeQS4mfUBm/AuFP5Pg=", "fULErWjky59hBtxXJjcJohdxonUTwdNMKSPgHWR3tik=", "evifTWcA8zikINadS4EcvyPfzmE+eRUCz2HAVHRP8+g=", "yezcCWbhGdET+qhoTMXp6sEbrH8DtdYwpm4NCeJyrQw=", "hJD9mYhG4i1WCJk5U9WYQb24nKp2bBCuwMHaWwftbqo=", "5T0/GxHVeThA+rA5L7+ux5Nhd6ePhhzfT6o4Pf8yHLA=", "xgOwodHfXn8Brymh2Nbueq44j72skllHBTiaWfAMcjk=", "eD53iIyc2MKK8GFW60DEyDHNwNWtBYtllwIrp8l8SPc=", "vQsjOULsCMRDM7bnz6l/RYB8uGdrPY0m7CnEIRLWwuk=", "HnYYjMQ4Gx5YVFOCpHdBOtY9/nf4nJIMOH21DhYT7ck=", "zet6hnkmYdd2LraCfCeLMKLXLL7sXhPsF1ACgJb3lM0=", "gifVKon0Qt8r8crhV1laG5fgLiV03mZL8lwWM9NF3so=", "RHfCt2SQ+W+u1fkuRgkoS3ol+H6hYTzx8DL2LKWVooI=", "foep0/WAmTLwWQNhmN0F3AxH4kByX4UBRmVP0W1vMts=", "B96Jo9iBuI67ouhGyRuFHs0fjwfCEy8ROnPSmGxlfwk=", "17/ONFOY/UJWJSKRLjeXBYt1ozwRpwVRTcx2afX6Jig=", "YHwLxO3byOokQbUFnVU5fEE1MEwWxIXCFOcre5pONuU=", "Pe7YUKzkLlGKJq+zgKsawazeHhGqc494r45vAwvFb+A=", "sigLUexvlcLRPbq/FiMj6Dg77XdykVMOidXkj9Ebnbg=", "0vr610bjPqz0tYoQbAyR8dAO9CbkvB1EqlodGMfBSVs=", "mkiZ3wukrr0VNeJWhratg3wlHyYIA8krD5drpHTffgA=", "0o5ch82Smsc7XINZKsTHHM8dz8p0olU1mj3DpejOu+E=", "R9x4+VZpgetFYWcqdtwrddbQYZeVGepHdvW7f/bsczk=", "tLT4R9SMykOOmFDCZRbX2wne2TJ1j9sac0zIZZDqdRc=", "TWUv4JW6Hon4gAAfKjWWZT9e3CEFrz1+koZY4rfwNEI=", "d0PnLdEtoKPCV3GmWGoeHZ3HpUfFKZewbHS7Dx2LxGI=", "E3Sdh0BtzdnrkR5ynVPAE/6NfEdtWATV0IKwDQHbrsc=", "yYZvGuFAkSIzVPYSZCEax9P8R4AOeI8ZvkWvVNbtRVg=", "QY7sQH1Xw7LyN9acbH0HxZAyVxVMdJzqSRBHoQXx/1Q=", "f1072H1+az/KvyI81HBvYz40g4ClVdRiWblrjmMJYaM=", "eGn7wD5yMOAaZ45y+2s1qha05n8FtybzIVahzbZp5Dk=", "E0to/YLkIO7sHrtcB0qjwNpVcvKFsgpXfUkHBbosqTA=", "4GfTBZdEP6qILxogNNN3fWVS63R6TIeJbeuKlSutO28=", "eFUYtWPHZOm1Iqt3QWaKPxOvH00F5ZqJjyA7p7kU8Uk="], "CachedAssets": {"9kmoBB9xfGW/OWXGuYyjP4Eg1BjXGAUZkGBaoy0aU+w=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\kxxxf7l9rz-b9sayid5wm.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "css/site#[.{fingerprint=b9sayid5wm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\css\\site.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "0suozzrugb", "Integrity": "0FC1RbVkLkwKhJ1oi+r2Hw4Tm9av6DyJeo95YVvfA3Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\css\\site.css", "FileLength": 318, "LastWriteTime": "2025-10-08T14:24:56.576074+00:00"}, "94rjdp+2/c0uLi3LXv3fnlFYElxYYMtej8Zey+kOTRM=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\nwoxsxi7jq-61n19gt1b8.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "favicon#[.{fingerprint=61n19gt1b8}]?.ico.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\favicon.ico", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nezsohjtde", "Integrity": "wfFuuYm+Lh6RbCeeiUqxw334b/hIOkp5j9eokGUM1Y0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\favicon.ico", "FileLength": 2468, "LastWriteTime": "2025-10-08T14:24:56.5770828+00:00"}, "DsIMJcdsJASfpfN3kQTeYlQEddK/TDFkWAvLwbSkeBU=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\fc179t9ebo-xtxxf3hu2r.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "js/site#[.{fingerprint=xtxxf3hu2r}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\js\\site.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "rl5dcbfpcw", "Integrity": "Ydq7YvQUzhbo68waLZeQvZnPOvmTeQ+HyDqjkA1whsA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\js\\site.js", "FileLength": 189, "LastWriteTime": "2025-10-08T14:24:56.5810818+00:00"}, "ng+w+FdS1b6TOc5EVkDlr3dZJ2XD6raZGPjoT1T9BUw=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\41ro834mhx-bqjiyaj88i.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint=bqjiyaj88i}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yf1pcrzlgs", "Integrity": "jhvPrvWZn8BbeR49W+r+MLNcnTeFyaSXxry9n1ctwy4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 6745, "LastWriteTime": "2025-10-08T14:24:56.5940831+00:00"}, "aPtFphY7KtAnkwaexMMIzxC5xmVGYfrTWSoBFxrC64Y=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\ks09104tnq-c2jlpeoesf.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint=c2jlpeoesf}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "goh6yfn9uv", "Integrity": "ALPf6qsZMu+Ui8l8jPJJF3MhTcq6uwrQhRWeWJL4ixU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 32794, "LastWriteTime": "2025-10-08T14:24:56.5870736+00:00"}, "YZUSOnP98cRpbYqDKBcpjOFwg4pvA9FDxyoNIxZ9EQY=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\ov7e9ynsv1-erw9l3u2r3.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint=erw9l3u2r3}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ff2i3b225l", "Integrity": "y2vSlIdcL+ImKFhcBMT5ujdAP1cyOZlHZK434Aiu0KA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 5969, "LastWriteTime": "2025-10-08T14:24:56.6010807+00:00"}, "vpcSOf6r6jA5f+rpnWd1GwB93vQvZHnEDb2ipk77SC0=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\iq37l7j61k-aexeepp0ev.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint=aexeepp0ev}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p2onhk81wj", "Integrity": "oRq8VZWOZX9mMbVVZBzw8rSxg8D8d6u0L0zM8MMIvaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 13807, "LastWriteTime": "2025-10-08T14:24:56.6010807+00:00"}, "8adGv6ELmAXWDGvGrRMwsPo9Bcat+C7hBmRKMLd/Glk=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\72mkgt740o-d7shbmvgxk.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint=d7shbmvgxk}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "td9xh3ux7u", "Integrity": "P5V7Xl3ceCLw6wDeOyAezE6gOa9re3B7gTUN/H/cDsY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 6749, "LastWriteTime": "2025-10-08T14:24:56.6030825+00:00"}, "cEasw6qk1kf3OZyYOpVId2513NJCZS80RoNWm+d9ffo=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\qou44q41od-ausgxo2sd3.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint=ausgxo2sd3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "vo1c50q1ou", "Integrity": "cWfRgogdfOCr54Ae/lxY515FP4TJyUwc4Ae6uejBPI0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 32793, "LastWriteTime": "2025-10-08T14:24:56.5800826+00:00"}, "3pSOXME/0oLFLsVI2CXzrW7F6z4n8oCaTzM/RCmY2kA=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\mnpvkuv9ml-k8d9w2qqmf.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint=k8d9w2qqmf}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yj3hsx47l", "Integrity": "ujhFfu7SIw4cL8FWI0ezmD29C7bGSesJvlEcySm5beY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 5971, "LastWriteTime": "2025-10-08T14:24:56.5920804+00:00"}, "7VEk2i+xZcx/cYpMDhdQ6VjNJEJZNqq4nkdPY1ND2Jc=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\0q1mih4e4n-cosvhxvwiu.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint=cosvhxvwiu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "d7q4cn5biw", "Integrity": "V0pKRwbw4DysvYMPCNK3s+wSdDLvMWJFO+hKrptop7A=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 13815, "LastWriteTime": "2025-10-08T14:24:56.6065981+00:00"}, "RPSgvhoUMry/taCl8JyXQCZdTUXPerUZOnBri1qCvlA=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\m9p5vdipk8-ub07r2b239.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint=ub07r2b239}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4tzbrq9c6f", "Integrity": "+EIaQ03ZHgfVopnrJFjz7ZgQSAO9GeMOK+bzccTIQyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 3380, "LastWriteTime": "2025-10-08T14:24:56.604597+00:00"}, "W/uj+59IDBdqQM53eIw7sdjH9uSzGUbpeuU7wT8gEN0=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\7gwk9xk2zm-fvhpjtyr6v.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint=fvhpjtyr6v}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "yedk7y2ovv", "Integrity": "FjCFey27wknG6tewZOhPfnDgvIw+sTBly7wTSXKSd8M=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 25821, "LastWriteTime": "2025-10-08T14:24:56.6115974+00:00"}, "rQOHFqY+4B5PR71oUBP+sC7lt+FEQzFa5tsbg0H+KHA=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\qsuxlqut3r-b7pk76d08c.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint=b7pk76d08c}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "r79n6nskqp", "Integrity": "Z9B/f6Ax/2JeBbNo3F1oaFvmOzRvs3yS0nnykt272Wc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 3213, "LastWriteTime": "2025-10-08T14:24:56.6238618+00:00"}, "ycDN0xlY22p7+7+rmoRWtS2GRJjVEvKhSBzq78WyJpk=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\ylqonvmf8c-fsbi9cje9m.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint=fsbi9cje9m}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2wjbbjit2u", "Integrity": "crByaO07mpJWuBndE5BbjmmKh3fj/Y0m8CmJDe+c3UQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 12587, "LastWriteTime": "2025-10-08T14:24:56.6281647+00:00"}, "rMLaP9qOPJQ5ZfGuEHXNyz++l/eG6TVsnu8oT59Vxug=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\x7xkf768vg-rzd6atqjts.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint=rzd6atqjts}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ukcr8jbv0r", "Integrity": "bhwmNaLC/7dOUfZxpXsnreiNsp2lbllRXrZLXndYFgA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 3367, "LastWriteTime": "2025-10-08T14:24:56.6075981+00:00"}, "2fC+4W+2zySISJbkZA77tjdqI/uGnQ1ED7c498s83f0=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\b3u2f7crz7-ee0r1s7dh0.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint=ee0r1s7dh0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bxpwye2e51", "Integrity": "wbodRxtgYaqKj+oSPNLIcKGMPziM41to/lFFylYjPBc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 25833, "LastWriteTime": "2025-10-08T14:24:56.5780823+00:00"}, "gbpxIeLZwXBP3S7181EPF89w7BzYzea/7RGIhidSl2s=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\mop3qtzpzb-dxx9fxp4il.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint=dxx9fxp4il}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "cv8bd5rjwi", "Integrity": "qNJnRAEMymFdtmi8gvc2VbVtDDk/UjeSnp+VQ10+cl0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 3246, "LastWriteTime": "2025-10-08T14:24:56.5880805+00:00"}, "lqxhbE8FPKAjEhzjaMKQkc3pcCDOmd/nSZeGW3y+sPE=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\zvk61lojzm-jd9uben2k1.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint=jd9uben2k1}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "l17cqhtmgf", "Integrity": "V8PtmOQL2VKj+/TynmuZNdDDyU9qG1QJ7FFnxVcN6Y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 15054, "LastWriteTime": "2025-10-08T14:24:56.6020804+00:00"}, "nol5sVQJSRb1SxC8QvoOKkQ0pYGrPua/+2VUDbIM2Sg=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\0rmbmpossy-khv3u5hwcm.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint=khv3u5hwcm}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "bub42mguo1", "Integrity": "8+5oxr92QYcYeV3zAk8RS4XmZo6Y5i3ZmbiJXj9KVQo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 11991, "LastWriteTime": "2025-10-08T14:24:56.6085964+00:00"}, "tsEMH1FKhL55pAA584U2K09SaKgSb22FM1qsRpv2FSs=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\lkq1hb8knm-r4e9w2rdcm.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint=r4e9w2rdcm}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "mns2a2zywm", "Integrity": "kj9zDkFgjDpUKwWasvv6a42LMVfqKjl/Ji5Z5LCNoRE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 44123, "LastWriteTime": "2025-10-08T14:24:56.6167241+00:00"}, "kXyfCQvt10Hc9w2yHwTRtYRrNLh398s5K8Sggyu3ryw=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\9n5qiuams4-lcd1t2u6c8.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint=lcd1t2u6c8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "iqwesyid6h", "Integrity": "xp5LPZ0vlqmxQrG+KjPm7ijhhJ+gD7VeH35lOUwBTWM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 11063, "LastWriteTime": "2025-10-08T14:24:56.6248619+00:00"}, "ICkvz2F9bEZp8V7YTpaYnIAN6k7di+iMAZK/7/rtX9M=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\4i3l9h08ql-c2oey78nd0.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint=c2oey78nd0}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "q71g5sacw1", "Integrity": "Jh/LtOdwAMNcT5vpbDEGsxe6Xv18LR1JqK4h/+hvs5g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 24341, "LastWriteTime": "2025-10-08T14:24:56.6321647+00:00"}, "LSENTkC0KenHjU/rHsMDWa9nkrLzsuWxn64AUst4XA4=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\jtoxbiihhi-tdbxkamptv.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint=tdbxkamptv}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "c9nucnnawf", "Integrity": "QAnDcxiLhrclwEVeKtd/GREdZNbXO2rZP5agorcS5EM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 11933, "LastWriteTime": "2025-10-08T14:24:56.6137256+00:00"}, "WwjRg3bsWggSXHVrlhukzoiV0/QHmiDyZxoFSQ/I4xY=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\4j1zfi5a5d-j5mq2jizvt.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint=j5mq2jizvt}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "nd4sjw69va", "Integrity": "KxjxxNhsaUlb9m0XwKNiMkNh6OuNbjdGXY0bmR5CTyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 44095, "LastWriteTime": "2025-10-08T14:24:56.5800826+00:00"}, "Jjl8aeOk3FdnLRO/hGXUYRFOoUwCBro7/rofMox8FxI=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\gq04aq0co6-06098lyss8.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint=06098lyss8}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1dromj56xs", "Integrity": "hXLxKxNQS6hkMWOc1Po5uxTK8ovzNg0xvRC9wMKOZiM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 11046, "LastWriteTime": "2025-10-08T14:24:56.5960793+00:00"}, "n4T5Zn/Mg/WZX17t9idtCPkOKMyhlN+RB+8rD2oAcNY=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\opr2u0b1r1-nvvlpmu67g.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint=nvvlpmu67g}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "oaf6lwhfh1", "Integrity": "8BfOknNd4oMU2u3DUY0C/Sjhoh3NGtdqE8kxApMdM2w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 24293, "LastWriteTime": "2025-10-08T14:24:56.6035892+00:00"}, "HzZJvNFr3ob2dZzSLwSJV8zJvNN9hum/fx0tnR1dDWc=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\rjdpucp8ik-s35ty4nyc5.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint=s35ty4nyc5}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jw1qs72mo9", "Integrity": "I0QuKxdK89NxyamT6EeIfl/MyifdDw+D8cUjkiXwoOU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 33251, "LastWriteTime": "2025-10-08T14:24:56.6105963+00:00"}, "rsrDpvIthtwxQjTeh8b6R0hmQP3S2Q7tZFjnS8UUtPc=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\mmkg8fypnb-pj5nd1wqec.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint=pj5nd1wqec}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1jikaxfu3u", "Integrity": "M4d5aODk+LnhCUggc/Xb6RX+Jh4E7X4KN58JXJR757I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 115009, "LastWriteTime": "2025-10-08T14:24:56.6281647+00:00"}, "Aci2FO8qcNcoUSoChV4Z4ObyN+LdVL2h99H5QE1MF7w=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\ddiduq9555-46ein0sx1k.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint=46ein0sx1k}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9buzyrzsnb", "Integrity": "NWIxwejcHtJ5yvljTypwFQBimL4GY/TpmkwWCoiPk+o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 30963, "LastWriteTime": "2025-10-08T14:24:56.6424322+00:00"}, "dgz93bOjesu8p28KmBKAFlJ2AFeQS4mfUBm/AuFP5Pg=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\r3v1uoz0mn-v0zj4ognzu.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint=v0zj4ognzu}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "we7ylowkap", "Integrity": "+BDBQp6fX0jhehydJj3yEmXwPsq4ccmpRwJadVX8HUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 91807, "LastWriteTime": "2025-10-08T14:24:56.6576383+00:00"}, "fULErWjky59hBtxXJjcJohdxonUTwdNMKSPgHWR3tik=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\la3tyrsnm4-37tfw0ft22.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint=37tfw0ft22}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qhts6dsckw", "Integrity": "Tl7d+IXzMoFjiGRivA39XpNjGWA6jMfITy87ywcah6c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 33101, "LastWriteTime": "2025-10-08T14:24:56.6197244+00:00"}, "evifTWcA8zikINadS4EcvyPfzmE+eRUCz2HAVHRP8+g=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\5ocv2nnob9-hrwsygsryq.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint=hrwsygsryq}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hack831yxc", "Integrity": "xwBA3wRtW8i96gsexkmrLvL85Ad0ueCN6i7I23oFCMU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 114953, "LastWriteTime": "2025-10-08T14:24:56.5910807+00:00"}, "yezcCWbhGdET+qhoTMXp6sEbrH8DtdYwpm4NCeJyrQw=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\y18sgwnorl-pk9g2wxc8p.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint=pk9g2wxc8p}]?.css.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "ih1ajc97pa", "Integrity": "Bhl6D0ngVAgx68NwXp2DEDO390PSrA5dlFHCQXY4WgM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 30986, "LastWriteTime": "2025-10-08T14:24:56.6075981+00:00"}, "hJD9mYhG4i1WCJk5U9WYQb24nKp2bBCuwMHaWwftbqo=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\bjhhwclmyd-ft3s53vfgj.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint=ft3s53vfgj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "j9d5qn8h15", "Integrity": "mVddgYoZfee39UGvBjujrPfkX4g9o5fJQgtcRjDKhDc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 91702, "LastWriteTime": "2025-10-08T14:24:56.6248619+00:00"}, "5T0/GxHVeThA+rA5L7+ux5Nhd6ePhhzfT6o4Pf8yHLA=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\zayw1abijj-6cfz1n2cew.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint=6cfz1n2cew}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sh9sbpd29q", "Integrity": "8KHWfFCoPlSmLyTbXOoHUNBddvrRpRlyAbs4j5nKGKY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 44354, "LastWriteTime": "2025-10-08T14:24:56.6291642+00:00"}, "xgOwodHfXn8Brymh2Nbueq44j72skllHBTiaWfAMcjk=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\xt8a7liseb-6pdc2jztkx.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint=6pdc2jztkx}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "sgz3bfuucz", "Integrity": "tELcWYAIUkPDirIRIOTlom3Q4rdUDcA6PdYMCRE48xY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 92045, "LastWriteTime": "2025-10-08T14:24:56.6566377+00:00"}, "eD53iIyc2MKK8GFW60DEyDHNwNWtBYtllwIrp8l8SPc=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\4oc3bao2wv-493y06b0oq.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint=493y06b0oq}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "9t6fi9687k", "Integrity": "PUb7rj1jLHgIo7Hwm3lvukBcGKDry7n7W2fa1xrz+zY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 23984, "LastWriteTime": "2025-10-08T14:24:56.6596372+00:00"}, "vQsjOULsCMRDM7bnz6l/RYB8uGdrPY0m7CnEIRLWwuk=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\h5yzyhj986-iovd86k7lj.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint=iovd86k7lj}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "svb68clhd0", "Integrity": "WDZTOK9dQrex7lgPEZZ+JZhLPdAF5GijB8N4Mslft/0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 86956, "LastWriteTime": "2025-10-08T14:24:56.677796+00:00"}, "HnYYjMQ4Gx5YVFOCpHdBOtY9/nf4nJIMOH21DhYT7ck=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\wjhbrd7166-vr1egmr9el.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint=vr1egmr9el}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "jtj7j0yqni", "Integrity": "6QziFU3u5nXZAGW+7TwN4NhocqzFBknypP5iUK5YK8k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 28852, "LastWriteTime": "2025-10-08T14:24:56.6810939+00:00"}, "zet6hnkmYdd2LraCfCeLMKLXLL7sXhPsF1ACgJb3lM0=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\xccy7t63ys-kbrnm935zg.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint=kbrnm935zg}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qmaqe8uvdz", "Integrity": "UkbVh5EDjQ9ElFF2VidPUKfIufgj0++IuUkiaieELZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 64130, "LastWriteTime": "2025-10-08T14:24:56.5860738+00:00"}, "gifVKon0Qt8r8crhV1laG5fgLiV03mZL8lwWM9NF3so=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\swv54ydzf4-jj8uyg4cgr.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint=jj8uyg4cgr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "erkthljg5u", "Integrity": "WmNnoBgejwchZUYVA3o03QOAGOuMRS778DeXvhP6suo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 18635, "LastWriteTime": "2025-10-08T14:24:56.6020804+00:00"}, "RHfCt2SQ+W+u1fkuRgkoS3ol+H6hYTzx8DL2LKWVooI=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\umm32y8a8g-y7v9cxd14o.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint=y7v9cxd14o}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "klzs96wner", "Integrity": "OOTujdl0QaxckSfKf4pISOdHdkWzUDKsaJmaS87CLzk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 56667, "LastWriteTime": "2025-10-08T14:24:56.6115974+00:00"}, "foep0/WAmTLwWQNhmN0F3AxH4kByX4UBRmVP0W1vMts=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\4snlr5wfie-notf2xhcfb.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint=notf2xhcfb}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4d5rpgxe6z", "Integrity": "6NzYRu+d/0puyGm6UFw/dwD9409WZbUx18xgnT/wQoQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 29569, "LastWriteTime": "2025-10-08T14:24:56.6228616+00:00"}, "B96Jo9iBuI67ouhGyRuFHs0fjwfCEy8ROnPSmGxlfwk=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\rb1hnudw9i-h1s4sie4z3.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint=h1s4sie4z3}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "42umladzh7", "Integrity": "rG54EGAHotdOswEyoMsIu5DDixozuuoKxcO7w4hcEQA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 64423, "LastWriteTime": "2025-10-08T14:24:56.6351931+00:00"}, "17/ONFOY/UJWJSKRLjeXBYt1ozwRpwVRTcx2afX6Jig=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\8qqncjuz6e-63fj8s7r0e.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint=63fj8s7r0e}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "on09t9pmch", "Integrity": "bIPgOT88ycSklHWxEzFQVPvNsgNbss3QQbyTqHho4PA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 16636, "LastWriteTime": "2025-10-08T14:24:56.6546372+00:00"}, "YHwLxO3byOokQbUFnVU5fEE1MEwWxIXCFOcre5pONuU=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\rnhlfa1tir-0j3bgjxly4.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint=0j3bgjxly4}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "n46fiwynw7", "Integrity": "OYFfBiMA8guQaokr7JUEKEquxVFRad17YsNbJdI0IKM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 55848, "LastWriteTime": "2025-10-08T14:24:56.6618899+00:00"}, "Pe7YUKzkLlGKJq+zgKsawazeHhGqc494r45vAwvFb+A=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\jz9g4wvsdw-47otxtyo56.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint=47otxtyo56}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "7mrxpa<PERSON><PERSON><PERSON>", "Integrity": "8WSd7G1SSJrTChp1H0sIwU3oPQXDsKXVF1KkXEF6LuI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 4651, "LastWriteTime": "2025-10-08T14:24:56.6281647+00:00"}, "sigLUexvlcLRPbq/FiMj6Dg77XdykVMOidXkj9Ebnbg=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\mb79hx6pmv-4v8eqarkd7.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint=4v8eqarkd7}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "kw5m9o3hck", "Integrity": "ZECRGyeVrglmY0fxP9wVujqjjSoecu3G7roYlvaINus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 2207, "LastWriteTime": "2025-10-08T14:24:56.576074+00:00"}, "0vr610bjPqz0tYoQbAyR8dAO9CbkvB1EqlodGMfBSVs=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\4rrnfqd5f2-356vix0kms.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint=356vix0kms}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "2yhmugv22u", "Integrity": "WpL0kbwDHONKwUu+fAC6nKQXtZOBY20Gsspcn336Iz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 694, "LastWriteTime": "2025-10-08T14:24:56.5830768+00:00"}, "mkiZ3wukrr0VNeJWhratg3wlHyYIA8krD5drpHTffgA=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\zqhtvm52di-83jwlth58m.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint=83jwlth58m}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "8j9rvpuvlg", "Integrity": "BIw6qCD4ysNHqPUgCkXJ/h+XlQ/h+2MuYEbvs/ZN+kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 14078, "LastWriteTime": "2025-10-08T14:24:56.5940831+00:00"}, "0o5ch82Smsc7XINZKsTHHM8dz8p0olU1mj3DpejOu+E=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\juv5s92ezk-mrlpezrjn3.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint=mrlpezrjn3}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "uya4g6am6w", "Integrity": "hhhjkgaWyJk2NWrF/LyhTIDOF42U+kBX72IU2K4RWJg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 6482, "LastWriteTime": "2025-10-08T14:24:56.6095963+00:00"}, "R9x4+VZpgetFYWcqdtwrddbQYZeVGepHdvW7f/bsczk=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\snq0hyl015-lzl9nlhx6b.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint=lzl9nlhx6b}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "anlp8nykfw", "Integrity": "KuJPTvhuArDs4mj2zziNxM+hBWFbOhi0BKQgeR1pHG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 14068, "LastWriteTime": "2025-10-08T14:24:56.6187249+00:00"}, "tLT4R9SMykOOmFDCZRbX2wne2TJ1j9sac0zIZZDqdRc=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\3fl6mrgnlx-ag7o75518u.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint=ag7o75518u}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hgmochx07c", "Integrity": "T/jNUyeLYdKQ3zndN8zs507jIg6GnkENMxuBqc1eXs0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 8121, "LastWriteTime": "2025-10-08T14:24:56.6258609+00:00"}, "TWUv4JW6Hon4gAAfKjWWZT9e3CEFrz1+koZY4rfwNEI=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\6m1qbojmrk-x0q3zqp4vz.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint=x0q3zqp4vz}]?.md.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "1fp9w2itex", "Integrity": "7bAe2yT3obcUnzDyFQyuh4GI4iC/NGB4Bs4gZaY3Wj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 683, "LastWriteTime": "2025-10-08T14:24:56.6301649+00:00"}, "d0PnLdEtoKPCV3GmWGoeHZ3HpUfFKZewbHS7Dx2LxGI=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\qbtb0j0sfg-0i3buxo5is.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint=0i3buxo5is}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "slhp14mdwv", "Integrity": "YPYGuVoBNkA8pP/JjlyjSON9Q7Ej1gVEUkbHrQT3uJU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 84431, "LastWriteTime": "2025-10-08T14:24:56.6882847+00:00"}, "E3Sdh0BtzdnrkR5ynVPAE/6NfEdtWATV0IKwDQHbrsc=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\ai3hbyjwf7-o1o13a6vjx.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=o1o13a6vjx}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "4efnowp72v", "Integrity": "rJUWNDEom3xIYh3mH5HCPpyh/CGhP0YhaSsaX6IOiDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 30683, "LastWriteTime": "2025-10-08T14:24:56.6281647+00:00"}, "yYZvGuFAkSIzVPYSZCEax9P8R4AOeI8ZvkWvVNbtRVg=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\y75a6685oj-ttgo8qnofa.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint=ttgo8qnofa}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "hq2hiirxit", "Integrity": "GfL7M76amKU+B+H1o4WVh7q13GpiC7L6scp3ODOz3ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 54456, "LastWriteTime": "2025-10-08T14:24:56.6351931+00:00"}, "QY7sQH1Xw7LyN9acbH0HxZAyVxVMdJzqSRBHoQXx/1Q=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\q9dl8gz2hl-2z0ns9nrw6.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint=2z0ns9nrw6}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "xlmiz42j7j", "Integrity": "9bWYPof03vxzc0YRmjwb6berDC7SXtE8rlvXxhIlakE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 68601, "LastWriteTime": "2025-10-08T14:24:56.6469382+00:00"}, "f1072H1+az/KvyI81HBvYz40g4ClVdRiWblrjmMJYaM=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\zaibw51wpd-muycvpuwrr.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=muycvpuwrr}]?.js.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "trtvcfsjjx", "Integrity": "h68a4ojlMeHZ2MJNcpHGvBgERdoUw7Qpxk6a/oPD5kQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 24360, "LastWriteTime": "2025-10-08T14:24:56.6526391+00:00"}, "eGn7wD5yMOAaZ45y+2s1qha05n8FtybzIVahzbZp5Dk=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\x8cixzpupz-87fc7y1x7t.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint=87fc7y1x7t}]?.map.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "p5ev1mo4sb", "Integrity": "bZLUSM9fRhNJKF4yFjKvv9iU/j2aC1lJtEqR9u5n6y8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 43123, "LastWriteTime": "2025-10-08T14:24:56.6566377+00:00"}, "E0to/YLkIO7sHrtcB0qjwNpVcvKFsgpXfUkHBbosqTA=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\c72llji1hu-mlv21k5csn.gz", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint=mlv21k5csn}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\LICENSE.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "k2cjzt041l", "Integrity": "JKWeHUaANNuffeRatdc54UCb84RoxjEw/nTq8Mops8Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 682, "LastWriteTime": "2025-10-08T14:24:56.6576383+00:00"}, "4GfTBZdEP6qILxogNNN3fWVS63R6TIeJbeuKlSutO28=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\ar4waq60pf-w6u16jdrff.gz", "SourceId": "ClockWebClient", "SourceType": "Computed", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "ClockWebClient#[.{fingerprint=w6u16jdrff}]?.styles.css.gz", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\scopedcss\\bundle\\ClockWebClient.styles.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o0yoxrbsl4", "Integrity": "yIDnphSmopuT71+oWaSbtH6UfsgqLq9dNQJkKm5E82I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\scopedcss\\bundle\\ClockWebClient.styles.css", "FileLength": 543, "LastWriteTime": "2025-10-08T14:24:56.6586391+00:00"}, "eFUYtWPHZOm1Iqt3QWaKPxOvH00F5ZqJjyA7p7kU8Uk=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\gvt5afvya8-w6u16jdrff.gz", "SourceId": "ClockWebClient", "SourceType": "Computed", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/ClockWebClient", "RelativePath": "ClockWebClient#[.{fingerprint=w6u16jdrff}]!.bundle.scp.css.gz", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Alternative", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\ClockWebClient.bundle.scp.css", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "o0yoxrbsl4", "Integrity": "yIDnphSmopuT71+oWaSbtH6UfsgqLq9dNQJkKm5E82I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\obj\\Debug\\net9.0\\scopedcss\\projectbundle\\ClockWebClient.bundle.scp.css", "FileLength": 543, "LastWriteTime": "2025-10-08T14:24:56.6596372+00:00"}}, "CachedCopyCandidates": {}}