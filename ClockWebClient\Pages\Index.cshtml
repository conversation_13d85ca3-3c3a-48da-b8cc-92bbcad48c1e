﻿@page
@model IndexModel
@{
    ViewData["Title"] = "gRPC Clock";
}

<div class="text-center">
    <h1 class="display-4">gRPC Live Clock</h1>
    <div class="clock-container mt-5">
        <div id="clock-display" class="clock-display">
            <div class="time-display">Loading...</div>
            <div class="timezone-display">UTC</div>
        </div>

        <div class="timezone-selector mt-4">
            <label for="timezone-select" class="form-label">Select Timezone:</label>
            <select id="timezone-select" class="form-select" style="max-width: 300px; margin: 0 auto;">
                <option value="UTC">UTC</option>
                <option value="America/New_York">Eastern Time</option>
                <option value="America/Chicago">Central Time</option>
                <option value="America/Denver">Mountain Time</option>
                <option value="America/Los_Angeles">Pacific Time</option>
                <option value="Europe/London">London</option>
                <option value="Europe/Paris">Paris</option>
                <option value="Asia/Tokyo">Tokyo</option>
                <option value="Asia/Shanghai">Shanghai</option>
                <option value="Australia/Sydney">Sydney</option>
            </select>
        </div>

        <div class="controls mt-4">
            <button id="start-btn" class="btn btn-success me-2">Start Live Clock</button>
            <button id="stop-btn" class="btn btn-danger me-2" disabled>Stop Live Clock</button>
            <button id="refresh-btn" class="btn btn-primary">Get Current Time</button>
        </div>

        <div id="status" class="mt-3 text-muted">
            Click "Start Live Clock" to begin streaming time from gRPC server
        </div>
    </div>
</div>

<style>
    .clock-container {
        max-width: 600px;
        margin: 0 auto;
        padding: 2rem;
        border: 2px solid #dee2e6;
        border-radius: 15px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .clock-display {
        background: #212529;
        color: #00ff00;
        padding: 2rem;
        border-radius: 10px;
        font-family: 'Courier New', monospace;
        box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .time-display {
        font-size: 3rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
        text-shadow: 0 0 10px #00ff00;
    }

    .timezone-display {
        font-size: 1.2rem;
        opacity: 0.8;
    }

    @@media (max-width: 768px) {
        .time-display {
            font-size: 2rem;
        }
        .clock-container {
            padding: 1rem;
        }
    }
</style>

<script>
    let eventSource = null;
    let isStreaming = false;

    const timeDisplay = document.getElementById('clock-display').querySelector('.time-display');
    const timezoneDisplay = document.getElementById('clock-display').querySelector('.timezone-display');
    const startBtn = document.getElementById('start-btn');
    const stopBtn = document.getElementById('stop-btn');
    const refreshBtn = document.getElementById('refresh-btn');
    const timezoneSelect = document.getElementById('timezone-select');
    const status = document.getElementById('status');

    function updateStatus(message) {
        status.textContent = message;
    }

    function updateClock(time, timezone) {
        timeDisplay.textContent = time;
        timezoneDisplay.textContent = timezone;
    }

    async function getCurrentTime() {
        try {
            const timezone = timezoneSelect.value;
            const response = await fetch(`/api/clock/current?timezone=${encodeURIComponent(timezone)}`);

            if (response.ok) {
                const data = await response.json();
                updateClock(data.formattedTime, data.timezone);
                updateStatus('Current time retrieved successfully');
            } else {
                updateStatus('Error getting current time');
            }
        } catch (error) {
            updateStatus('Error: ' + error.message);
        }
    }

    function startStreaming() {
        if (isStreaming) return;

        const timezone = timezoneSelect.value;
        eventSource = new EventSource(`/api/clock/stream?timezone=${encodeURIComponent(timezone)}`);

        eventSource.onopen = function() {
            isStreaming = true;
            startBtn.disabled = true;
            stopBtn.disabled = false;
            timezoneSelect.disabled = true;
            updateStatus('Connected to live clock stream');
        };

        eventSource.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                updateClock(data.formattedTime, data.timezone);
                updateStatus('Live clock running...');
            } catch (error) {
                updateStatus('Error parsing time data');
            }
        };

        eventSource.onerror = function() {
            updateStatus('Connection error - attempting to reconnect...');
        };
    }

    function stopStreaming() {
        if (eventSource) {
            eventSource.close();
            eventSource = null;
        }

        isStreaming = false;
        startBtn.disabled = false;
        stopBtn.disabled = true;
        timezoneSelect.disabled = false;
        updateStatus('Live clock stopped');
    }

    // Event listeners
    startBtn.addEventListener('click', startStreaming);
    stopBtn.addEventListener('click', stopStreaming);
    refreshBtn.addEventListener('click', getCurrentTime);

    timezoneSelect.addEventListener('change', function() {
        if (isStreaming) {
            stopStreaming();
            setTimeout(startStreaming, 100);
        }
    });

    // Get initial time
    getCurrentTime();
</script>
