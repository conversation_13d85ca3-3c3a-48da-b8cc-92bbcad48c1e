{"runtimeTarget": {"name": ".NETCoreApp,Version=v8.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v8.0": {"GrpcServiceLearning/1.0.0": {"dependencies": {"Grpc.AspNetCore": "2.57.0"}, "runtime": {"GrpcServiceLearning.dll": {}}}, "Google.Protobuf/3.23.1": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.23.1.0", "fileVersion": "3.23.1.0"}}}, "Grpc.AspNetCore/2.57.0": {"dependencies": {"Google.Protobuf": "3.23.1", "Grpc.AspNetCore.Server.ClientFactory": "2.57.0", "Grpc.Tools": "2.57.0"}}, "Grpc.AspNetCore.Server/2.57.0": {"dependencies": {"Grpc.Net.Common": "2.57.0"}, "runtime": {"lib/net8.0/Grpc.AspNetCore.Server.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.57.0.0"}}}, "Grpc.AspNetCore.Server.ClientFactory/2.57.0": {"dependencies": {"Grpc.AspNetCore.Server": "2.57.0", "Grpc.Net.ClientFactory": "2.57.0"}, "runtime": {"lib/net8.0/Grpc.AspNetCore.Server.ClientFactory.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.57.0.0"}}}, "Grpc.Core.Api/2.57.0": {"runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.57.0.0"}}}, "Grpc.Net.Client/2.57.0": {"dependencies": {"Grpc.Net.Common": "2.57.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0"}, "runtime": {"lib/net8.0/Grpc.Net.Client.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.57.0.0"}}}, "Grpc.Net.ClientFactory/2.57.0": {"dependencies": {"Grpc.Net.Client": "2.57.0", "Microsoft.Extensions.Http": "6.0.0"}, "runtime": {"lib/net8.0/Grpc.Net.ClientFactory.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.57.0.0"}}}, "Grpc.Net.Common/2.57.0": {"dependencies": {"Grpc.Core.Api": "2.57.0"}, "runtime": {"lib/net8.0/Grpc.Net.Common.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.57.0.0"}}}, "Grpc.Tools/2.57.0": {}, "Microsoft.Extensions.DependencyInjection/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {}, "Microsoft.Extensions.Http/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0"}}, "Microsoft.Extensions.Logging/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "6.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "System.Diagnostics.DiagnosticSource": "6.0.0"}}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {}, "Microsoft.Extensions.Options/6.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "6.0.0"}}, "Microsoft.Extensions.Primitives/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Diagnostics.DiagnosticSource/6.0.0": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}}}, "libraries": {"GrpcServiceLearning/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Google.Protobuf/3.23.1": {"type": "package", "serviceable": true, "sha512": "sha512-ebVTazjarSS1MsKjFxq4a3Kudm3dq2KjM9jcxgn2NdeRaCl1tvarqgqQRITs99kg8M+B7SvwuD8yqcwE+PoN4g==", "path": "google.protobuf/3.23.1", "hashPath": "google.protobuf.3.23.1.nupkg.sha512"}, "Grpc.AspNetCore/2.57.0": {"type": "package", "serviceable": true, "sha512": "sha512-mTKo9d5DYOxTFhg/8sdX5O3+gw1uFESmu7WhH04t5PSPltpQ10ZiK+Mr/ju/TW3MwxJg8uHt71fWL6AS443izw==", "path": "grpc.aspnetcore/2.57.0", "hashPath": "grpc.aspnetcore.2.57.0.nupkg.sha512"}, "Grpc.AspNetCore.Server/2.57.0": {"type": "package", "serviceable": true, "sha512": "sha512-g4JKqJ3RB7WKOrBxMSCkRc/1Xa5GqYlVb7k8OjUsERKW7LdSQ0ajcFMZ4Q79BZgA/xbJlGttmq4ewyzpBqGetA==", "path": "grpc.aspnetcore.server/2.57.0", "hashPath": "grpc.aspnetcore.server.2.57.0.nupkg.sha512"}, "Grpc.AspNetCore.Server.ClientFactory/2.57.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ucl50YKMMRaP/ikx20v6tDGdNaRzeiujmw8kmAFy3/4f8+d1mDY8wn3vcXpIflX4IE+WvZQOTHz5r/3/GutbQw==", "path": "grpc.aspnetcore.server.clientfactory/2.57.0", "hashPath": "grpc.aspnetcore.server.clientfactory.2.57.0.nupkg.sha512"}, "Grpc.Core.Api/2.57.0": {"type": "package", "serviceable": true, "sha512": "sha512-48jpfMyYdhaPS6PGnk7sEqSO3+c/w11ahY+a33ymo0BsQEsBVIoJoS5xMsl5RzBlpKY41PnN+qJGMm3VyE5OQg==", "path": "grpc.core.api/2.57.0", "hashPath": "grpc.core.api.2.57.0.nupkg.sha512"}, "Grpc.Net.Client/2.57.0": {"type": "package", "serviceable": true, "sha512": "sha512-vVDW0ePnaH9Na+wjm64vpzdj05JEjOCmAXUlANVc+s3hBBreEizfbmlzWPjsL6ZskaSrA59Ckb0J2l1HMvtMHQ==", "path": "grpc.net.client/2.57.0", "hashPath": "grpc.net.client.2.57.0.nupkg.sha512"}, "Grpc.Net.ClientFactory/2.57.0": {"type": "package", "serviceable": true, "sha512": "sha512-t3+mz7z6e3tezZbekioGOqix4QcOAWRACQqP3v/FA6HxkgraTRceaREPaFTxSFvGrpPtOSkJ3y1WRx0MA+8v9g==", "path": "grpc.net.clientfactory/2.57.0", "hashPath": "grpc.net.clientfactory.2.57.0.nupkg.sha512"}, "Grpc.Net.Common/2.57.0": {"type": "package", "serviceable": true, "sha512": "sha512-babJpsAwVH3lPjg8cvWGqW+Fos1RN+cqudfkW2EyB2DK2v5yjD2axZ1Uay6kR6ec2/p9ljodktrfoJ2lJ/pwnQ==", "path": "grpc.net.common/2.57.0", "hashPath": "grpc.net.common.2.57.0.nupkg.sha512"}, "Grpc.Tools/2.57.0": {"type": "package", "serviceable": true, "sha512": "sha512-H/R3AqavrrWTPsKeEQjSlXFIkqaZ4gMVmtL6dzLg00xup1Fx9jZT9ACxGDv3t83HMmazJLM5rO3k2jfT4Ox58Q==", "path": "grpc.tools/2.57.0", "hashPath": "grpc.tools.2.57.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-k6PWQMuoBDGGHOQTtyois2u4AwyVcIwL2LaSLlTZQm2CYcJ1pxbt6jfAnpWmzENA/wfrYRI/X9DTLoUkE4AsLw==", "path": "microsoft.extensions.dependencyinjection/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Http/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-15+pa2G0bAMHbHewaQIdr/y6ag2H3yh4rd9hTXavtWDzQBkvpe2RMqFg8BxDpcQWssmjmBApGPcw93QRz6YcMg==", "path": "microsoft.extensions.http/6.0.0", "hashPath": "microsoft.extensions.http.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eIbyj40QDg1NDz0HBW0S5f3wrLVnKWnDJ/JtZ+yJDFnDj90VoPuoPmFkeaXrtu+0cKm5GRAwoDf+dBWXK0TUdg==", "path": "microsoft.extensions.logging/6.0.0", "hashPath": "microsoft.extensions.logging.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/HggWBbTwy8TgebGSX5DBZ24ndhzi93sHUBDvP1IxbZD7FDokYzdAr6+vbWGjw2XAfR2EJ1sfKUotpjHnFWPxA==", "path": "microsoft.extensions.logging.abstractions/6.0.0", "hashPath": "microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dzXN0+V1AyjOe2xcJ86Qbo233KHuLEY0njf/P2Kw8SfJU+d45HNS2ctJdnEnrWbM9Ye2eFgaC5Mj9otRMU6IsQ==", "path": "microsoft.extensions.options/6.0.0", "hashPath": "microsoft.extensions.options.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Primitives/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-9+PnzmQFfEFNR9J2aDTfJGGupShHjOuGw4VUv+JB044biSHrnmCIMD+mJHmb2H7YryrfBEXDurxQ47gJZdCKNQ==", "path": "microsoft.extensions.primitives/6.0.0", "hashPath": "microsoft.extensions.primitives.6.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-frQDfv0rl209cKm1lnwTgFPzNigy2EKk1BS3uAvHvlBVKe5cymGyHO+Sj+NLv5VF/AhHsqPIUUwya5oV4CHMUw==", "path": "system.diagnostics.diagnosticsource/6.0.0", "hashPath": "system.diagnostics.diagnosticsource.6.0.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}}}