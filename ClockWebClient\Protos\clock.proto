syntax = "proto3";

option csharp_namespace = "ClockWebClient";

package clock;

import "google/protobuf/timestamp.proto";

// The clock service definition.
service Clock {
  // Gets the current time
  rpc GetCurrentTime (TimeRequest) returns (TimeResponse);
  
  // Streams the current time continuously
  rpc StreamTime (TimeRequest) returns (stream TimeResponse);
}

// The request message for time operations.
message TimeRequest {
  // Optional timezone (e.g., "UTC", "America/New_York")
  string timezone = 1;
}

// The response message containing the current time.
message TimeResponse {
  // Current timestamp
  google.protobuf.Timestamp timestamp = 1;
  
  // Formatted time string
  string formatted_time = 2;
  
  // Timezone used
  string timezone = 3;
}
