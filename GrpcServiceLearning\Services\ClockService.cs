using Grpc.Core;
using Google.Protobuf.WellKnownTypes;

namespace GrpcServiceLearning.Services;

public class ClockService : Clock.ClockBase
{
    private readonly ILogger<ClockService> _logger;

    public ClockService(ILogger<ClockService> logger)
    {
        _logger = logger;
    }

    public override Task<TimeResponse> GetCurrentTime(TimeRequest request, ServerCallContext context)
    {
        var now = DateTime.UtcNow;
        var timezone = string.IsNullOrEmpty(request.Timezone) ? "UTC" : request.Timezone;

        // Convert to specified timezone if provided
        DateTime localTime = now;
        if (!string.IsNullOrEmpty(request.Timezone) && request.Timezone != "UTC")
        {
            try
            {
                var timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(request.Timezone);
                localTime = TimeZoneInfo.ConvertTimeFromUtc(now, timeZoneInfo);
            }
            catch (TimeZoneNotFoundException)
            {
                _logger.LogWarning("Timezone {Timezone} not found, using UTC", request.Timezone);
                timezone = "UTC";
            }
        }

        var response = new TimeResponse
        {
            Timestamp = Timestamp.FromDateTime(now),
            FormattedTime = localTime.ToString("yyyy-MM-dd HH:mm:ss"),
            Timezone = timezone
        };

        _logger.LogInformation("Returning current time: {Time} ({Timezone})", response.FormattedTime, timezone);
        return Task.FromResult(response);
    }

    public override async Task StreamTime(TimeRequest request, IServerStreamWriter<TimeResponse> responseStream, ServerCallContext context)
    {
        var timezone = string.IsNullOrEmpty(request.Timezone) ? "UTC" : request.Timezone;
        TimeZoneInfo? timeZoneInfo = null;

        if (!string.IsNullOrEmpty(request.Timezone) && request.Timezone != "UTC")
        {
            try
            {
                timeZoneInfo = TimeZoneInfo.FindSystemTimeZoneById(request.Timezone);
            }
            catch (TimeZoneNotFoundException)
            {
                _logger.LogWarning("Timezone {Timezone} not found, using UTC", request.Timezone);
                timezone = "UTC";
            }
        }

        _logger.LogInformation("Starting time stream for timezone: {Timezone}", timezone);

        while (!context.CancellationToken.IsCancellationRequested)
        {
            var now = DateTime.UtcNow;
            DateTime localTime = now;

            if (timeZoneInfo != null)
            {
                localTime = TimeZoneInfo.ConvertTimeFromUtc(now, timeZoneInfo);
            }

            var response = new TimeResponse
            {
                Timestamp = Timestamp.FromDateTime(now),
                FormattedTime = localTime.ToString("yyyy-MM-dd HH:mm:ss"),
                Timezone = timezone
            };

            try
            {
                await responseStream.WriteAsync(response);
                await Task.Delay(1000, context.CancellationToken); // Send update every second
            }
            catch (OperationCanceledException)
            {
                _logger.LogInformation("Time stream cancelled");
                break;
            }
        }
    }
}
