{"version": 2, "dgSpecHash": "ZvaXAng52r8=", "success": true, "projectFilePath": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\ClockWebClient.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\google.protobuf\\3.32.1\\google.protobuf.3.32.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.core.api\\2.71.0\\grpc.core.api.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.net.client\\2.71.0\\grpc.net.client.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.net.common\\2.71.0\\grpc.net.common.2.71.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\grpc.tools\\2.72.0\\grpc.tools.2.72.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\6.0.0\\microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512"], "logs": [{"code": "NU1900", "level": "Warning", "message": "Error occurred while getting package vulnerability data: Unable to load the service index for source https://nuget.telerik.com/v3/index.json.", "projectPath": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\ClockWebClient.csproj", "warningLevel": 1, "filePath": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\ClockWebClient.csproj", "targetGraphs": []}]}