using Grpc.Net.Client;
using ClockWebClient;
using System.Runtime.CompilerServices;

namespace ClockWebClient.Services;

public class ClockGrpcService
{
    private readonly GrpcChannel _channel;
    private readonly Clock.ClockClient _client;

    public ClockGrpcService(IConfiguration configuration)
    {
        var grpcServerUrl = configuration["GrpcServer:Url"] ?? "https://localhost:7001";
        _channel = GrpcChannel.ForAddress(grpcServerUrl);
        _client = new Clock.ClockClient(_channel);
    }

    public async Task<TimeResponse> GetCurrentTimeAsync(string? timezone = null)
    {
        var request = new TimeRequest { Timezone = timezone ?? "UTC" };
        return await _client.GetCurrentTimeAsync(request);
    }

    public async IAsyncEnumerable<TimeResponse> StreamTimeAsync(string? timezone = null, [EnumeratorCancellation] CancellationToken cancellationToken = default)
    {
        var request = new TimeRequest { Timezone = timezone ?? "UTC" };
        
        using var call = _client.StreamTime(request, cancellationToken: cancellationToken);

        while (await call.ResponseStream.MoveNext(cancellationToken))
        {
            yield return call.ResponseStream.Current;
        }
    }

    public void Dispose()
    {
        _channel?.Dispose();
    }
}
