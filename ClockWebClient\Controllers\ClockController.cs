using Microsoft.AspNetCore.Mvc;
using ClockWebClient.Services;
using System.Text.Json;

namespace ClockWebClient.Controllers;

[ApiController]
[Route("api/[controller]")]
public class ClockController : ControllerBase
{
    private readonly ClockGrpcService _clockService;
    private readonly ILogger<ClockController> _logger;

    public ClockController(ClockGrpcService clockService, ILogger<ClockController> logger)
    {
        _clockService = clockService;
        _logger = logger;
    }

    [HttpGet("current")]
    public async Task<IActionResult> GetCurrentTime([FromQuery] string? timezone = null)
    {
        try
        {
            var response = await _clockService.GetCurrentTimeAsync(timezone);
            
            var result = new
            {
                formattedTime = response.FormattedTime,
                timezone = response.Timezone,
                timestamp = response.Timestamp?.ToDateTime()
            };
            
            return Ok(result);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting current time");
            return StatusCode(500, new { error = "Failed to get current time" });
        }
    }

    [HttpGet("stream")]
    public async Task StreamTime([FromQuery] string? timezone = null, CancellationToken cancellationToken = default)
    {
        Response.Headers["Content-Type"] = "text/event-stream";
        Response.Headers["Cache-Control"] = "no-cache";
        Response.Headers["Connection"] = "keep-alive";

        try
        {
            await foreach (var timeResponse in _clockService.StreamTimeAsync(timezone, cancellationToken))
            {
                var data = new
                {
                    formattedTime = timeResponse.FormattedTime,
                    timezone = timeResponse.Timezone,
                    timestamp = timeResponse.Timestamp?.ToDateTime()
                };

                var json = JsonSerializer.Serialize(data);
                await Response.WriteAsync($"data: {json}\n\n");
                await Response.Body.FlushAsync();

                if (cancellationToken.IsCancellationRequested)
                    break;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in time stream");
            await Response.WriteAsync($"event: error\ndata: {ex.Message}\n\n");
        }
    }
}
