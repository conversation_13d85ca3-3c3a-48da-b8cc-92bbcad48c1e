{"GlobalPropertiesHash": "3Dq0L72cWRDYS4uhXlygEBCgQ8Nr/fTKECEDBBwsA0A=", "FingerprintPatternsHash": "gq3WsqcKBUGTSNle7RKKyXRIwh7M8ccEqOqYvIzoM04=", "PropertyOverridesHash": "8ZRc1sGeVrPBx4lD717BgRaQekyh78QKV9SKsdt638U=", "InputHashes": ["5VmUrolL8rX6CmXdarmZW4kbzUCWD6BaTP8NrvBIwoM=", "YspTF1NCipT0+9XuzfIw+c7qPykusaOP0xp028B+kuM=", "4O6OUwYjpFpLSrKhQ9Nvdkh4cpCsbAFMW3K5ykvehO8=", "k2ILcBKoTM/Kx3bZSo+VFg5HEko8tuEPapkPO+/dvRo=", "CZv3vmxOijTfu5Ii5j5YZDcJfWCpv4tEZTABJVq2zfU=", "WoiHsv59yJcmdXe6eJtrrPk+aAL98EVn+lM3OczvfE0=", "FztgTnCdMrWQqP0Bf0cLGyVrv4dYUnbGeP18nB6EHtw=", "zv7d6f3eYAlrNXWjayL/VkovjBp0qjsaxebrZ9CnRH8=", "NHyjB8hpTJl4NMS4vO+4MaLth/7yy/WA1uyToEuFVf4=", "1wLqjyY5naqhblLaALNXBPwqcfK7775hPxOonRyKtaA=", "GdSzDS5LCHaQDTblG+PlQ5MCRI6k7pacQv4R603oFaQ=", "jO2IK2q6NJ0VhsrUzWvRbQRnDcPZbuBa3LW1iukamdI=", "xFRJGHI50ClsB0JCCrhrkZsXz9ymPl1t3EL5RiT9QFA=", "ShJJzvmFkOYTFUyIftgApZwJYlz8FTgZNctIkpaus+0=", "5IY161z9EHPOQgx6rA0rN+5KBXpkZUeuCN1jwMvifSQ=", "yLyCy5QPRA/Uo/ZN24jiyx8F1DlVTasykaSVP4tqlBo=", "5UenSi4V6YTXXtnhmfde5p4ZdoXv46x0P7OP8AY85ok=", "9BRoQtEVvm8AKfCn9GMQFpwN0/4cFKPOkJ89DgLwZzk=", "gyL4CzgMkGxVJAS0OSTz4+GVY516ouTba0rrRv/yYNM=", "mJGEEqCnC7qSV2nYD71RIW3nEMVrJjkKNnhSTxlxEXc=", "1xXjU9x3CohmF77CONjVjywzszLz8Uk+m479rj4k/ew=", "BwOJ/DqlIHsTPQ9fgDY8RYhR2KMyWt147c+vdkHT+/U=", "pDr7gQprdAGnwJwNOrng626KazH4zR+gOXlpymVPbaQ=", "dQJXetGzIUjcs9P3fOcD1LzCy5CV4ZlACyMle5JwTGU=", "VSdMxIdHzYybrg1df9Ea6GAZ+p887cXuQCFed7/Log4=", "ViYSNqZu4TUpyg6LiwoiIrqyG5RiF6N9gKVi+UFcWeU=", "Aa/IrhpcM62hd4DZ+RlMA5a9+yK2SqfKKEGDsHDbn0o=", "WHtnm8c0hr57k/6kyAVhXmo0Prl6fhvUZ8bDuO0ET14=", "bPCkLtf68VODPS3cf64kayyGtbStouC8j1TTEHEWXOA=", "JFW9qeZKeyg03xPIBDHmXyfUGE0QRGN4IceOHqHfNME=", "WSQ/asvZeo/expqYfK9T+ma9ZfT0rhHBt5jaPXnbBNg=", "ntS+pj8xGzfJp7QONWTyay+OcYPHL4dNseJrBIL3jGU=", "nIpzSVjIL5glDmIQu+zOJnQ8/P79XFj72eU8wND6LoE=", "cx6wNhDkksUBGshDj0VXUywlVr1Lt/7nhd4olyx3FwM=", "bamRcuQdGbdYkjXWE5+7Cxd+19gJMxwd/XvKaNglG+E=", "2HPBcICJLhBm3cjy15OmYW8BTvUz0wN8l56U2OFpq4c=", "2SCfmRLYvIq76B+1m2SXSAE0AX58JqurplnHKFMgti8=", "sb0zN8KUYasGXnvuYNZtSyBbD6zQYOzi2OGpIbaDSWg=", "ozqqKgK1ZHvU1FxvftzBgN9bPq3tunl/Zl3XCqGdCzk=", "C2bbVHTn/qhBV0BtfoO4Okim108Y6ITSIvosPPLcluw=", "jqSE2l/oN6/5ccnulWQk0y8r9L2p4isVaYuGp2ZJrJ0=", "DJTWE04EkBsNCQy/Ft62Ad+TYG7XJ5HP+yIJ+z9zWUU=", "Q0xu3JdFXpH6KkvBBPE11w9cbwHRvq1Av0hG5ofXsLw=", "tW91+coMp0cQicjAPLtUT7jJ3wCmKpQM9uXB3KrHHWQ=", "ygf9FEWD+TTcFEqj4bsWKkdE/aZvw/TdBB8CUUomokw=", "/myokpRz0ySka8Wt/A7ocuGO0aG9mc8AM5bTVHiqOs4=", "SPbo6ahX+0OtpZ/OE/pQglac+9hGxZyqsslniiUcuAA=", "P/Od4Rs+0FbIS0j1pm2HXzK326EOY0uUFAwyoEg0gMA=", "xXqWlaPy3y+K+KLEHBVkEGULwLPmD7VaQRtrmE5T1Cc=", "Y1+WILZfHY4LQ1TEeRmCOWSfPFowh4G8qW/T8dZfsxM=", "7GFCdMVZVcGG1W7Gdm+qJxAQLVK3Fm4hRBinnR+YfTY=", "peLl2t8MLwXLra0LWHM1HXRNTV0GTDqbC08HFXqdKwU=", "HBLfNdWFIJdAQOl3NHmQeBc5I8qcx7EsM7TbHBQFPSQ=", "a/qxvC96IQmgPigE3A55Uuc1/OOxaWfbMc08WRzjKgY=", "xkDCjRYa1ivDNZXV0muzBjkkGcsxniNzcS6E30DFuVw=", "clWhatz2J1shPlniz+Af+ATlB6/8q167w1zp5iKTBFo=", "+zW/gvh2dckZ2dz7BHS0Umw1kI2IVqrcl2TSl4xBdGE=", "5jDo6RXLbOFZAfg4lSrfNxCw2b0hqwTtID1URxbCA/M=", "AU/mWyWmClQ2nfnFSdLn3VHKqbJzD22dm4Bzfih0rXA=", "PzxILkEY/MDFGCG1GShQEZFH0hsFrMUHfAkwgBlLbEM=", "jj/srtwZq6RhJBottw/1tcM+pbOGm3ymvDoIWQM3/Xs=", "W5K7Kq6w7lDSXmHYOvFdmzag0vTjGZ4CMk0UotL1Hr8=", "38VcQCekgHsuM3IBenuP4WeQPzlGKfZ5v2nCO/3juuQ=", "LGyMYyfENgiekzLMOTvbqlqakOiXA2w6GuC4fWtRDjw=", "mBFA75dHji4r3VaSaDB+49UaRfFHI6N5d+ECBTtHIXM=", "NNSI9Xmv6Btcp/qmC0zQHDWjSU1nrL//g7t0SzPqSwo=", "7afeRJB6ZT97PnhaYJpCmizIF8A4t23+uWJYliMX05k=", "DIQH2RRYfR2Y3iFY2wGZIxQCskL4XPg8Z50PScm3hbw=", "gFU9CE3KIkk1RRBuyagtEr8DEhBW+4tt8XsHyVASREs=", "r+oVHe78JE/JNZkEMnLgnPuXXOdcCCcf4XcdOHMu92M=", "jnVVSknWyzruCniOyKmnsRQushwytcZgUQMgbTTsKAQ=", "sWX8QKoRIsn5uEKnsn5ixW0+R+6l8i4RXYztd71+OkM="], "CachedAssets": {"5VmUrolL8rX6CmXdarmZW4kbzUCWD6BaTP8NrvBIwoM=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\css\\site.css", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b9sayid5wm", "Integrity": "j6fhJSuuyLpOSLuPJU0TsDV0iNjor5S3rDnvxJrt4bg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 667, "LastWriteTime": "2025-10-08T14:19:28.103499+00:00"}, "YspTF1NCipT0+9XuzfIw+c7qPykusaOP0xp028B+kuM=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\favicon.ico", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-10-08T14:19:28.2924665+00:00"}, "4O6OUwYjpFpLSrKhQ9Nvdkh4cpCsbAFMW3K5ykvehO8=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\js\\site.js", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "xtxxf3hu2r", "Integrity": "hRQyftXiu1lLX2P9Ly9xa4gHJgLeR1uGN5qegUobtGo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 231, "LastWriteTime": "2025-10-08T14:19:28.1660023+00:00"}, "k2ILcBKoTM/Kx3bZSo+VFg5HEko8tuEPapkPO+/dvRo=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-10-08T14:19:27.9227351+00:00"}, "CZv3vmxOijTfu5Ii5j5YZDcJfWCpv4tEZTABJVq2zfU=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-10-08T14:19:27.9247359+00:00"}, "WoiHsv59yJcmdXe6eJtrrPk+aAL98EVn+lM3OczvfE0=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-10-08T14:19:27.9257366+00:00"}, "FztgTnCdMrWQqP0Bf0cLGyVrv4dYUnbGeP18nB6EHtw=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-10-08T14:19:27.9267454+00:00"}, "zv7d6f3eYAlrNXWjayL/VkovjBp0qjsaxebrZ9CnRH8=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-10-08T14:19:27.930743+00:00"}, "NHyjB8hpTJl4NMS4vO+4MaLth/7yy/WA1uyToEuFVf4=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-10-08T14:19:27.9635212+00:00"}, "1wLqjyY5naqhblLaALNXBPwqcfK7775hPxOonRyKtaA=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-10-08T14:19:27.9929783+00:00"}, "GdSzDS5LCHaQDTblG+PlQ5MCRI6k7pacQv4R603oFaQ=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-10-08T14:19:28.0169023+00:00"}, "jO2IK2q6NJ0VhsrUzWvRbQRnDcPZbuBa3LW1iukamdI=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-10-08T14:19:28.0191756+00:00"}, "xFRJGHI50ClsB0JCCrhrkZsXz9ymPl1t3EL5RiT9QFA=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-10-08T14:19:28.0202553+00:00"}, "ShJJzvmFkOYTFUyIftgApZwJYlz8FTgZNctIkpaus+0=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-10-08T14:19:28.0212658+00:00"}, "5IY161z9EHPOQgx6rA0rN+5KBXpkZUeuCN1jwMvifSQ=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-10-08T14:19:28.0222638+00:00"}, "yLyCy5QPRA/Uo/ZN24jiyx8F1DlVTasykaSVP4tqlBo=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-10-08T14:19:28.0222638+00:00"}, "5UenSi4V6YTXXtnhmfde5p4ZdoXv46x0P7OP8AY85ok=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-10-08T14:19:28.0232753+00:00"}, "9BRoQtEVvm8AKfCn9GMQFpwN0/4cFKPOkJ89DgLwZzk=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-10-08T14:19:28.0242873+00:00"}, "gyL4CzgMkGxVJAS0OSTz4+GVY516ouTba0rrRv/yYNM=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-10-08T14:19:28.0252884+00:00"}, "mJGEEqCnC7qSV2nYD71RIW3nEMVrJjkKNnhSTxlxEXc=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-10-08T14:19:28.0262869+00:00"}, "1xXjU9x3CohmF77CONjVjywzszLz8Uk+m479rj4k/ew=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-10-08T14:19:28.0272959+00:00"}, "BwOJ/DqlIHsTPQ9fgDY8RYhR2KMyWt147c+vdkHT+/U=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-10-08T14:19:28.0272959+00:00"}, "pDr7gQprdAGnwJwNOrng626KazH4zR+gOXlpymVPbaQ=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-10-08T14:19:28.0286731+00:00"}, "dQJXetGzIUjcs9P3fOcD1LzCy5CV4ZlACyMle5JwTGU=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-10-08T14:19:28.0286731+00:00"}, "VSdMxIdHzYybrg1df9Ea6GAZ+p887cXuQCFed7/Log4=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-10-08T14:19:28.0296812+00:00"}, "ViYSNqZu4TUpyg6LiwoiIrqyG5RiF6N9gKVi+UFcWeU=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-10-08T14:19:28.0306817+00:00"}, "Aa/IrhpcM62hd4DZ+RlMA5a9+yK2SqfKKEGDsHDbn0o=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-10-08T14:19:28.0369112+00:00"}, "WHtnm8c0hr57k/6kyAVhXmo0Prl6fhvUZ8bDuO0ET14=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "s35ty4nyc5", "Integrity": "GKEF18s44B5e0MolXAkpkqLiEbOVlKf6VyYr/G/E6pw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css", "FileLength": 281046, "LastWriteTime": "2025-10-08T14:19:28.0389133+00:00"}, "bPCkLtf68VODPS3cf64kayyGtbStouC8j1TTEHEWXOA=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pj5nd1wqec", "Integrity": "KzNVR3p7UZGba94dnCtlc6jXjK5urSPiZ/eNnKTmDkw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.css.map", "FileLength": 679755, "LastWriteTime": "2025-10-08T14:19:28.044428+00:00"}, "JFW9qeZKeyg03xPIBDHmXyfUGE0QRGN4IceOHqHfNME=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "46ein0sx1k", "Integrity": "PI8n5gCcz9cQqQXm3PEtDuPG8qx9oFsFctPg0S5zb8g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css", "FileLength": 232803, "LastWriteTime": "2025-10-08T14:19:28.0454266+00:00"}, "WSQ/asvZeo/expqYfK9T+ma9ZfT0rhHBt5jaPXnbBNg=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "v0zj4ognzu", "Integrity": "8SM4U2NQpCLGTQLW5D/x3qSTwxVq2CP+GXYc3V1WwFs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.min.css.map", "FileLength": 589892, "LastWriteTime": "2025-10-08T14:19:28.0469415+00:00"}, "ntS+pj8xGzfJp7QONWTyay+OcYPHL4dNseJrBIL3jGU=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-10-08T14:19:28.0484481+00:00"}, "nIpzSVjIL5glDmIQu+zOJnQ8/P79XFj72eU8wND6LoE=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-10-08T14:19:28.0504551+00:00"}, "cx6wNhDkksUBGshDj0VXUywlVr1Lt/7nhd4olyx3FwM=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-10-08T14:19:28.0539995+00:00"}, "bamRcuQdGbdYkjXWE5+7Cxd+19gJMxwd/XvKaNglG+E=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-10-08T14:19:28.0550075+00:00"}, "2HPBcICJLhBm3cjy15OmYW8BTvUz0wN8l56U2OFpq4c=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-10-08T14:19:28.0570077+00:00"}, "2SCfmRLYvIq76B+1m2SXSAE0AX58JqurplnHKFMgti8=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-10-08T14:19:28.0590073+00:00"}, "sb0zN8KUYasGXnvuYNZtSyBbD6zQYOzi2OGpIbaDSWg=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-10-08T14:19:28.0600078+00:00"}, "ozqqKgK1ZHvU1FxvftzBgN9bPq3tunl/Zl3XCqGdCzk=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-10-08T14:19:28.0610081+00:00"}, "C2bbVHTn/qhBV0BtfoO4Okim108Y6ITSIvosPPLcluw=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-10-08T14:19:28.0630083+00:00"}, "jqSE2l/oN6/5ccnulWQk0y8r9L2p4isVaYuGp2ZJrJ0=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-10-08T14:19:28.0640088+00:00"}, "DJTWE04EkBsNCQy/Ft62Ad+TYG7XJ5HP+yIJ+z9zWUU=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-10-08T14:19:28.0640088+00:00"}, "Q0xu3JdFXpH6KkvBBPE11w9cbwHRvq1Av0hG5ofXsLw=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-10-08T14:19:28.0658863+00:00"}, "tW91+coMp0cQicjAPLtUT7jJ3wCmKpQM9uXB3KrHHWQ=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-10-08T14:19:28.0691316+00:00"}, "ygf9FEWD+TTcFEqj4bsWKkdE/aZvw/TdBB8CUUomokw=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-10-08T14:19:28.0709268+00:00"}, "/myokpRz0ySka8Wt/A7ocuGO0aG9mc8AM5bTVHiqOs4=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-10-08T14:19:28.0709268+00:00"}, "SPbo6ahX+0OtpZ/OE/pQglac+9hGxZyqsslniiUcuAA=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-10-08T14:19:28.0725086+00:00"}, "P/Od4Rs+0FbIS0j1pm2HXzK326EOY0uUFAwyoEg0gMA=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-10-08T14:19:28.0735153+00:00"}, "xXqWlaPy3y+K+KLEHBVkEGULwLPmD7VaQRtrmE5T1Cc=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-10-08T14:19:28.2944643+00:00"}, "Y1+WILZfHY4LQ1TEeRmCOWSfPFowh4G8qW/T8dZfsxM=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-10-08T14:19:28.2944643+00:00"}, "7GFCdMVZVcGG1W7Gdm+qJxAQLVK3Fm4hRBinnR+YfTY=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-10-08T14:19:28.075518+00:00"}, "peLl2t8MLwXLra0LWHM1HXRNTV0GTDqbC08HFXqdKwU=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-10-08T14:19:28.2558534+00:00"}, "HBLfNdWFIJdAQOl3NHmQeBc5I8qcx7EsM7TbHBQFPSQ=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-10-08T14:19:28.2648589+00:00"}, "a/qxvC96IQmgPigE3A55Uuc1/OOxaWfbMc08WRzjKgY=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-10-08T14:19:28.291466+00:00"}, "xkDCjRYa1ivDNZXV0muzBjkkGcsxniNzcS6E30DFuVw=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-10-08T14:19:28.2934644+00:00"}, "clWhatz2J1shPlniz+Af+ATlB6/8q167w1zp5iKTBFo=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-10-08T14:19:28.0745165+00:00"}, "+zW/gvh2dckZ2dz7BHS0Umw1kI2IVqrcl2TSl4xBdGE=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-10-08T14:19:28.1701309+00:00"}, "5jDo6RXLbOFZAfg4lSrfNxCw2b0hqwTtID1URxbCA/M=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-10-08T14:19:28.1732163+00:00"}, "AU/mWyWmClQ2nfnFSdLn3VHKqbJzD22dm4Bzfih0rXA=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-10-08T14:19:28.1807104+00:00"}, "PzxILkEY/MDFGCG1GShQEZFH0hsFrMUHfAkwgBlLbEM=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-10-08T14:19:28.2191277+00:00"}, "jj/srtwZq6RhJBottw/1tcM+pbOGm3ymvDoIWQM3/Xs=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-10-08T14:19:28.223128+00:00"}, "W5K7Kq6w7lDSXmHYOvFdmzag0vTjGZ4CMk0UotL1Hr8=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-10-08T14:19:28.2271284+00:00"}, "38VcQCekgHsuM3IBenuP4WeQPzlGKfZ5v2nCO/3juuQ=": {"Identity": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "ClockWebClient", "SourceType": "Discovered", "ContentRoot": "D:\\Learnings\\ASP\\GrpcServiceLearning\\ClockWebClient\\wwwroot\\", "BasePath": "_content/ClockWebClient", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": null, "AssetMergeSource": "", "RelatedAsset": null, "AssetTraitName": null, "AssetTraitValue": null, "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-10-08T14:19:28.0745165+00:00"}}, "CachedCopyCandidates": {}}