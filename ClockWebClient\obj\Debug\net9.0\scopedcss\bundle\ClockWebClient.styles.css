/* _content/ClockWebClient/Pages/Shared/_Layout.cshtml.rz.scp.css */
/* Please see documentation at https://learn.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand[b-u7qv0uvqx1] {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

a[b-u7qv0uvqx1] {
  color: #0077cc;
}

.btn-primary[b-u7qv0uvqx1] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active[b-u7qv0uvqx1], .nav-pills .show > .nav-link[b-u7qv0uvqx1] {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.border-top[b-u7qv0uvqx1] {
  border-top: 1px solid #e5e5e5;
}
.border-bottom[b-u7qv0uvqx1] {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow[b-u7qv0uvqx1] {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy[b-u7qv0uvqx1] {
  font-size: 1rem;
  line-height: inherit;
}

.footer[b-u7qv0uvqx1] {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px;
}
