// <auto-generated>
//     Generated by the protocol buffer compiler.  DO NOT EDIT!
//     source: Protos/clock.proto
// </auto-generated>
#pragma warning disable 0414, 1591, 8981, 0612
#region Designer generated code

using grpc = global::Grpc.Core;

namespace GrpcServiceLearning {
  /// <summary>
  /// The clock service definition.
  /// </summary>
  public static partial class Clock
  {
    static readonly string __ServiceName = "clock.Clock";

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static void __Helper_SerializeMessage(global::Google.Protobuf.IMessage message, grpc::SerializationContext context)
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (message is global::Google.Protobuf.IBufferMessage)
      {
        context.SetPayloadLength(message.CalculateSize());
        global::Google.Protobuf.MessageExtensions.WriteTo(message, context.GetBufferWriter());
        context.Complete();
        return;
      }
      #endif
      context.Complete(global::Google.Protobuf.MessageExtensions.ToByteArray(message));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static class __Helper_MessageCache<T>
    {
      public static readonly bool IsBufferMessage = global::System.Reflection.IntrospectionExtensions.GetTypeInfo(typeof(global::Google.Protobuf.IBufferMessage)).IsAssignableFrom(typeof(T));
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static T __Helper_DeserializeMessage<T>(grpc::DeserializationContext context, global::Google.Protobuf.MessageParser<T> parser) where T : global::Google.Protobuf.IMessage<T>
    {
      #if !GRPC_DISABLE_PROTOBUF_BUFFER_SERIALIZATION
      if (__Helper_MessageCache<T>.IsBufferMessage)
      {
        return parser.ParseFrom(context.PayloadAsReadOnlySequence());
      }
      #endif
      return parser.ParseFrom(context.PayloadAsNewBuffer());
    }

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::GrpcServiceLearning.TimeRequest> __Marshaller_clock_TimeRequest = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::GrpcServiceLearning.TimeRequest.Parser));
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Marshaller<global::GrpcServiceLearning.TimeResponse> __Marshaller_clock_TimeResponse = grpc::Marshallers.Create(__Helper_SerializeMessage, context => __Helper_DeserializeMessage(context, global::GrpcServiceLearning.TimeResponse.Parser));

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::GrpcServiceLearning.TimeRequest, global::GrpcServiceLearning.TimeResponse> __Method_GetCurrentTime = new grpc::Method<global::GrpcServiceLearning.TimeRequest, global::GrpcServiceLearning.TimeResponse>(
        grpc::MethodType.Unary,
        __ServiceName,
        "GetCurrentTime",
        __Marshaller_clock_TimeRequest,
        __Marshaller_clock_TimeResponse);

    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    static readonly grpc::Method<global::GrpcServiceLearning.TimeRequest, global::GrpcServiceLearning.TimeResponse> __Method_StreamTime = new grpc::Method<global::GrpcServiceLearning.TimeRequest, global::GrpcServiceLearning.TimeResponse>(
        grpc::MethodType.ServerStreaming,
        __ServiceName,
        "StreamTime",
        __Marshaller_clock_TimeRequest,
        __Marshaller_clock_TimeResponse);

    /// <summary>Service descriptor</summary>
    public static global::Google.Protobuf.Reflection.ServiceDescriptor Descriptor
    {
      get { return global::GrpcServiceLearning.ClockReflection.Descriptor.Services[0]; }
    }

    /// <summary>Base class for server-side implementations of Clock</summary>
    [grpc::BindServiceMethod(typeof(Clock), "BindService")]
    public abstract partial class ClockBase
    {
      /// <summary>
      /// Gets the current time
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>The response to send back to the client (wrapped by a task).</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task<global::GrpcServiceLearning.TimeResponse> GetCurrentTime(global::GrpcServiceLearning.TimeRequest request, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

      /// <summary>
      /// Streams the current time continuously
      /// </summary>
      /// <param name="request">The request received from the client.</param>
      /// <param name="responseStream">Used for sending responses back to the client.</param>
      /// <param name="context">The context of the server-side call handler being invoked.</param>
      /// <returns>A task indicating completion of the handler.</returns>
      [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
      public virtual global::System.Threading.Tasks.Task StreamTime(global::GrpcServiceLearning.TimeRequest request, grpc::IServerStreamWriter<global::GrpcServiceLearning.TimeResponse> responseStream, grpc::ServerCallContext context)
      {
        throw new grpc::RpcException(new grpc::Status(grpc::StatusCode.Unimplemented, ""));
      }

    }

    /// <summary>Creates service definition that can be registered with a server</summary>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static grpc::ServerServiceDefinition BindService(ClockBase serviceImpl)
    {
      return grpc::ServerServiceDefinition.CreateBuilder()
          .AddMethod(__Method_GetCurrentTime, serviceImpl.GetCurrentTime)
          .AddMethod(__Method_StreamTime, serviceImpl.StreamTime).Build();
    }

    /// <summary>Register service method with a service binder with or without implementation. Useful when customizing the service binding logic.
    /// Note: this method is part of an experimental API that can change or be removed without any prior notice.</summary>
    /// <param name="serviceBinder">Service methods will be bound by calling <c>AddMethod</c> on this object.</param>
    /// <param name="serviceImpl">An object implementing the server-side handling logic.</param>
    [global::System.CodeDom.Compiler.GeneratedCode("grpc_csharp_plugin", null)]
    public static void BindService(grpc::ServiceBinderBase serviceBinder, ClockBase serviceImpl)
    {
      serviceBinder.AddMethod(__Method_GetCurrentTime, serviceImpl == null ? null : new grpc::UnaryServerMethod<global::GrpcServiceLearning.TimeRequest, global::GrpcServiceLearning.TimeResponse>(serviceImpl.GetCurrentTime));
      serviceBinder.AddMethod(__Method_StreamTime, serviceImpl == null ? null : new grpc::ServerStreamingServerMethod<global::GrpcServiceLearning.TimeRequest, global::GrpcServiceLearning.TimeResponse>(serviceImpl.StreamTime));
    }

  }
}
#endregion
